<!DOCTYPE html>
<html lang="en">

@include('visitors.includes.header')
<body>

    <div class="" id="preloader">
        <div class="img d-none">
            <img src="public/uploads/website-images/Spinner.gif" alt="UniFood" class="img-fluid">
        </div>
    </div>

    <!--=============================
        TOPBAR START
    ==============================-->
    @include('visitors.includes.topbar')
    <!--=============================
        TOPBAR END
    ==============================-->


    <!--=============================
        MENU START
    ==============================-->
 @include('visitors.includes.nav')

@yield('content')
    <!--=============================
        FOOTER START
    ==============================-->
   @include('visitors.includes.footer')
    <!--=============================
        FOOTER END
    ==============================-->


    <!--=============================
        SCROLL BUTTON START
    ==============================-->
    <div class="wsus__scroll_btn">
        Go to top
    </div>
    <!--=============================
        SCROLL BUTTON END
    ==============================-->

    <script type="text/javascript">
        var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
        (function(){
            var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
            s1.async=true;
            s1.src='https://embed.tawk.to/612dc781d6e7610a49b2d444/1fedd6l9m';
            s1.charset='UTF-8';
            s1.setAttribute('crossorigin','*');
            s0.parentNode.insertBefore(s1,s0);
        })();
    </script>




    <!--bootstrap js-->
    <script src="public/user/js/bootstrap.bundle.min.js"></script>
    <!--font-awesome js-->
    <script src="public/user/js/Font-Awesome.js"></script>
    <!-- slick slider -->
    <script src="public/user/js/slick.min.js"></script>
    <!-- isotop js -->
    <script src="public/user/js/isotope.pkgd.min.js"></script>
    <!-- simplyCountdownjs -->
    <script src="public/user/js/simplyCountdown.js"></script>
    <!-- counter up js -->
    <script src="public/user/js/jquery.waypoints.min.js"></script>
    <script src="public/user/js/jquery.countup.min.js"></script>
    <!-- nice select js -->
    <script src="public/user/js/jquery.nice-select.min.js"></script>
    <!-- venobox js -->
    <script src="public/user/js/venobox.min.js"></script>
    <!-- sticky sidebar js -->
    <script src="public/user/js/sticky_sidebar.js"></script>
    <!-- wow js -->
    <script src="public/user/js/wow.min.js"></script>
    <!-- ex zoom js -->
    <script src="public/user/js/jquery.exzoom.js"></script>

    <script src="public/backend/js/bootstrap-datepicker.min.js"></script>

    <!--main/custom js-->
    <script src="public/user/js/main.js"></script>

    <script src="public/toastr/toastr.min.js"></script>
    <script src="public/backend/js/select2.min.js"></script>

    <script>
    </script>


    <script>
        (function($) {
        "use strict";
        $(document).ready(function () {

            $("#setLanguage").on('change', function(e){
                this.submit();
            });

            $(".first_menu_product").click();

            $('.select2').select2();
            $('.modal_select2').select2({
                dropdownParent: $("#address_modal")
            });

            $('.datepicker').datepicker({
                format: 'yyyy-mm-dd',
                startDate: '-Infinity'
            });

            $(document).on('click', '.mini-item-remove', function () {
                let root_li = $(this).parents('li');
                let rowid = root_li.data('mini-item-rowid');
                root_li.remove();

                let is_cart_page = "no";
                if(is_cart_page == 'yes'){
                    $(".main-cart-item-"+rowid).remove();
                    calculate_total();
                }

                calculate_mini_total();

                $.ajax({
                    type: 'get',
                    url: "https://unifood.websolutionus.com/remove-cart-item" + "/" + rowid,
                    success: function (response) {
                        toastr.success(response.message);

                        let ready_to_reload = "no"
                        if(ready_to_reload == 'yes'){
                            window.location.reload();
                        }
                    },
                    error: function(response) {
                        if(response.status == 500){
                            toastr.error("Server error occured")
                        }

                        if(response.status == 403){
                            toastr.error("Server error occured")
                        }
                    }
                });
            });

            $("#subscribe_form").on('submit', function(e){
                    e.preventDefault();
                    var isDemo = "0"
                    if(isDemo == 0){
                        toastr.error('This Is Demo Version. You Can Not Change Anything');
                        return;
                    }

                    $("#subscribe_btn").prop("disabled", true);
                    $("#subscribe_btn").html(`<i class="fas fa-spinner"></i>`);

                    $.ajax({
                        type: 'POST',
                        data: $('#subscribe_form').serialize(),
                        url: "https://unifood.websolutionus.com/subscribe-request",
                        success: function (response) {
                            toastr.success(response.message)
                            $("#subscribe_form").trigger("reset");
                            $("#subscribe_btn").prop("disabled", false);
                            $("#subscribe_btn").html(`<i class="fas fa-paper-plane"></i>`);
                        },
                        error: function(response) {
                            $("#subscribe_btn").prop("disabled", false);
                            $("#subscribe_btn").html(`<i class="fas fa-paper-plane"></i>`);

                            if(response.status == 403){
                                if(response.responseJSON.message)toastr.error(response.responseJSON.message)
                            }
                        }
                    });
                })
        });
    })(jQuery);

    function calculate_mini_total(){
        let mini_sub_total = 0;
        let mini_total_item = 0;
        $(".mini-input-price").each(function () {
            let current_val = $(this).val();
            mini_sub_total = parseInt(mini_sub_total) + parseInt(current_val);
            mini_total_item = parseInt(mini_total_item) + parseInt(1);
        });

        $(".mini_sub_total").html(`$${mini_sub_total}`);
        $(".topbar_cart_qty").html(mini_total_item);
        $(".mini_cart_body_item").html(`Total Item(${mini_total_item})`);

        let mini_empty_cart = `<div class="wsus__menu_cart_header">
                <h5>Your shopping cart is empty!</h5>
                <span class="close_cart"><i class="fal fa-times"></i></span>
            </div>
            `;

        if(mini_total_item == 0){
            $(".wsus__menu_cart_boody").html(mini_empty_cart)
        }
    }

    function load_product_model(product_id){

        $("#preloader").addClass('preloader')
        $(".img").removeClass('d-none')

        $.ajax({
            type: 'get',
            url: "https://unifood.websolutionus.com/load-product-modal" + "/" + product_id,
            success: function (response) {
                $("#preloader").removeClass('preloader')
                $(".img").addClass('d-none')
                $(".load_product_modal_response").html(response)
                $("#cartModal").modal('show');
            },
            error: function(response) {
                toastr.error("Server error occured")
            }
        });
    }

    function add_to_wishlist(id){
        $.ajax({
            type: 'get',
            url: "https://unifood.websolutionus.com/add-to-wishlist" + "/" + id,
            success: function (response) {
                toastr.success("Wishlist added successfully");
            },
            error: function(response) {
                if(response.status == 500){
                    toastr.error("Server error occured")
                }

                if(response.status == 403){
                    toastr.error(response.responseJSON.message)
                }
            }
        });
    }
    function before_auth_wishlist(){
        toastr.error("Please login first")
    }

    </script>

</body>


<!-- Mirrored from unifood.websolutionus.com/ by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 08 Jul 2025 20:51:29 GMT -->

</html>
