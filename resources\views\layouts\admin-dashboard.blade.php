<!DOCTYPE html>
<html lang="en">

@include('visitors.includes.header')
<body>

    <div class="" id="preloader">
        <div class="img d-none">
            <img src="public/uploads/website-images/Spinner.gif" alt="UniFood" class="img-fluid">
        </div>
    </div>

    <!--=============================
        ADMIN TOPBAR START
    ==============================-->
    @include('layouts.admin-topbar')
    <!--=============================
        ADMIN TOPBAR END
    ==============================-->

    <!--=============================
        ADMIN NAVIGATION START
    ==============================-->
    @include('layouts.admin-nav')
    <!--=============================
        ADMIN NAVIGATION END
    ==============================-->

    <!--=============================
        ADMIN DASHBOARD CONTENT START
    ==============================-->
    <main class="admin-dashboard-content">
        @if(isset($header))
            <section class="admin-header-section">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <div class="admin-page-header">
                                {{ $header }}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        @endif

        <section class="admin-main-content">
            <div class="container">
                @yield('content')
            </div>
        </section>
    </main>
    <!--=============================
        ADMIN DASHBOARD CONTENT END
    ==============================-->

    <!--=============================
        FOOTER START
    ==============================-->
    @include('visitors.includes.footer')
    <!--=============================
        FOOTER END
    ==============================-->

    <!--=============================
        SCROLL BUTTON START
    ==============================-->
    <div class="wsus__scroll_btn">
        Go to top
    </div>
    <!--=============================
        SCROLL BUTTON END
    ==============================-->

    <!--bootstrap js-->
    <script src="public/public/user/js/bootstrap.bundle.min.js"></script>
    <!--font-awesome js-->
    <script src="public/public/user/js/Font-Awesome.js"></script>
    <!-- slick slider -->
    <script src="public/public/user/js/slick.min.js"></script>
    <!-- isotop js -->
    <script src="public/public/user/js/isotope.pkgd.min.js"></script>
    <!-- simplyCountdownjs -->
    <script src="public/public/user/js/simplyCountdown.js"></script>
    <!-- counter up js -->
    <script src="public/public/user/js/jquery.waypoints.min.js"></script>
    <script src="public/public/user/js/jquery.countup.min.js"></script>
    <!-- nice select js -->
    <script src="public/public/user/js/jquery.nice-select.min.js"></script>
    <!-- venobox js -->
    <script src="public/public/user/js/venobox.min.js"></script>
    <!-- sticky sidebar js -->
    <script src="public/public/user/js/sticky_sidebar.js"></script>
    <!-- wow js -->
    <script src="public/public/user/js/wow.min.js"></script>
    <!-- ex zoom js -->
    <script src="public/public/user/js/jquery.exzoom.js"></script>

    <script src="public/public/backend/js/bootstrap-datepicker.min.js"></script>

    <!--main/custom js-->
    <script src="public/public/user/js/main.js"></script>

    <script src="public/public/toastr/toastr.min.js"></script>
    <script src="public/public/backend/js/select2.min.js"></script>

    <script>
        (function($) {
            "use strict";
            $(document).ready(function () {
                // Admin dashboard specific JavaScript
                $('.select2').select2();
                
                $('.datepicker').datepicker({
                    format: 'yyyy-mm-dd',
                    startDate: '-Infinity'
                });

                // Admin dashboard animations
                $('.admin-stat-card').hover(function() {
                    $(this).addClass('admin-card-hover');
                }, function() {
                    $(this).removeClass('admin-card-hover');
                });
            });
        })(jQuery);
    </script>

    @stack('scripts')

</body>
</html>
