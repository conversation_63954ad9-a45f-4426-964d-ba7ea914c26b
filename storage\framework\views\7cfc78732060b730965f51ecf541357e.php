<!DOCTYPE html>
<html lang="en">

<?php echo $__env->make('visitors.includes.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<body>

    <div class="" id="preloader">
        <div class="img d-none">
            <img src="public/uploads/website-images/Spinner.gif" alt="UniFood" class="img-fluid">
        </div>
    </div>

    <!--=============================
        ADMIN TOPBAR START
    ==============================-->
    <?php echo $__env->make('layouts.admin-topbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!--=============================
        ADMIN TOPBAR END
    ==============================-->

    <!--=============================
        ADMIN NAVIGATION START
    ==============================-->
    <?php echo $__env->make('layouts.admin-nav', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!--=============================
        ADMIN NAVIGATION END
    ==============================-->

    <!--=============================
        ADMIN DASHBOARD CONTENT START
    ==============================-->
    <main class="admin-dashboard-content">
        <?php if(isset($header)): ?>
            <section class="admin-header-section">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <div class="admin-page-header">
                                <?php echo e($header); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </section>
        <?php endif; ?>

        <section class="admin-main-content">
            <div class="container">
                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </section>
    </main>
    <!--=============================
        ADMIN DASHBOARD CONTENT END
    ==============================-->

    <!--=============================
        FOOTER START
    ==============================-->
    <?php echo $__env->make('visitors.includes.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!--=============================
        FOOTER END
    ==============================-->

    <!--=============================
        SCROLL BUTTON START
    ==============================-->
    <div class="wsus__scroll_btn">
        Go to top
    </div>
    <!--=============================
        SCROLL BUTTON END
    ==============================-->

    <!--bootstrap js-->
    <script src="public/public/user/js/bootstrap.bundle.min.js"></script>
    <!--font-awesome js-->
    <script src="public/public/user/js/Font-Awesome.js"></script>
    <!-- slick slider -->
    <script src="public/public/user/js/slick.min.js"></script>
    <!-- isotop js -->
    <script src="public/public/user/js/isotope.pkgd.min.js"></script>
    <!-- simplyCountdownjs -->
    <script src="public/public/user/js/simplyCountdown.js"></script>
    <!-- counter up js -->
    <script src="public/public/user/js/jquery.waypoints.min.js"></script>
    <script src="public/public/user/js/jquery.countup.min.js"></script>
    <!-- nice select js -->
    <script src="public/public/user/js/jquery.nice-select.min.js"></script>
    <!-- venobox js -->
    <script src="public/public/user/js/venobox.min.js"></script>
    <!-- sticky sidebar js -->
    <script src="public/public/user/js/sticky_sidebar.js"></script>
    <!-- wow js -->
    <script src="public/public/user/js/wow.min.js"></script>
    <!-- ex zoom js -->
    <script src="public/public/user/js/jquery.exzoom.js"></script>

    <script src="public/public/backend/js/bootstrap-datepicker.min.js"></script>

    <!--main/custom js-->
    <script src="public/public/user/js/main.js"></script>

    <script src="public/public/toastr/toastr.min.js"></script>
    <script src="public/public/backend/js/select2.min.js"></script>

    <script>
        (function($) {
            "use strict";
            $(document).ready(function () {
                // Admin dashboard specific JavaScript
                $('.select2').select2();
                
                $('.datepicker').datepicker({
                    format: 'yyyy-mm-dd',
                    startDate: '-Infinity'
                });

                // Admin dashboard animations
                $('.admin-stat-card').hover(function() {
                    $(this).addClass('admin-card-hover');
                }, function() {
                    $(this).removeClass('admin-card-hover');
                });
            });
        })(jQuery);
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>

</body>
</html>
<?php /**PATH D:\Laravel-Apps\gfood\resources\views/layouts/admin-dashboard.blade.php ENDPATH**/ ?>